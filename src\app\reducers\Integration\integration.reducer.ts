import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  AddFacebookAccountSuccess,
  AddIntegrationGoogleLandingSuccess,
  AddIntegrationGoogleLeadSuccess,
  AddIntegrationSuccess,
  CommonIvrIntegrationSuccess,
  DoesExistAccountNameSuccess,
  FetchAgencyNameListSuccess,
  FetchAgentsSuccess,
  FetchExportFacebookStatusSuccess,
  FetchFacebookAccountsSuccess,
  FetchFacebookMarketingSuccess,
  FetchFbAccountFormsSuccess,
  FetchFbAccountSuccess,
  FetchFacebookAccountCampaignsSuccess,
  FetchFacebookCampaignMarketingSuccess,
  FetchGoogleIntegrationListSuccess,
  FetchIntegrationAssignmentDetailsSuccess,
  FetchIntegrationByIdSuccess,
  FetchIntegrationCountSuccess,
  FetchIntegrationListSuccess,
  FetchIntegrationProjectsSuccess,
  FetchIvrDetailsByIdSuccess,
  FetchIVRIntegrationCountSuccess,
  FetchIVRListSuccess,
  FetchIvrServiceProvidersSuccess,
  FetchSubscribedFormsSuccess,
  FetchVirtualNosSuccess,
  FetchVNAssignmentSuccess,
  FetchWebhookAccountSuccess,
  FetchWebhookSuccess,
  IntegrationActionTypes,
  IntegrationEmailSuccess,
  AssignPageSize,
  IvrIntegrationSuccess,
  ToggleFBSubscriptionSuccess,
} from 'src/app/reducers/Integration/integration.actions';

export interface IntegrationCount {
  Direct: LeadSourceCount;
  Housing: LeadSourceCount;
  MagicBricks: LeadSourceCount;
  NinetyNineAcers: LeadSourceCount;
  IVR: LeadSourceCount;
}

export interface LeadSourceCount {
  leadSource: number;
  accountCount: number;
  leadCount: number;
}
export interface IntegrationData {
  accountName: string;
  accountId: string;
  leadSource: number;
  leadCount: number;
}

export type IntegrationState = {
  doesAccountNameExists: boolean;
  webhook: any;
  webhookAccount: any,
  exportFacebookStatusLoading: boolean;
  webhookAccountsLoading: boolean
  exportFacebookStatus: any;
  integrationData?: IntegrationData[];
  activePropertyId?: string;
  integrationDetail?: IntegrationData[];
  integrationGoogleDetail?: any;
  integrationAccountData?: any;
  integrationCount?: any;
  ivrCount?: any;
  subscribedforms?: Array<any>;
  ivrIntegrationData?: IntegrationData[];
  ivrDetailsById: any;
  commonIvrData?: IntegrationData[];
  agents?: Array<any>;
  virtualNos?: Array<any>;
  VNAssignment?: any;
  ivrServiceProviders?: any;
  accounts: Array<any>;
  addFBAcountResult: boolean;
  integrationGoogleLandingData?: IntegrationData[];
  integrationGoogleLeadData?: IntegrationData[];
  assignedUserIds?: Array<string>;
  subscribedAds?: Array<any>;
  agencyNameList?: any;
  projects?: any;
  accountForms?: Array<any>;
  accountFormsIsLoading?: boolean;
  integrationListIsLoading?: boolean;
  googleIntegrationListIsLoading?: boolean;
  agencyNameListIsLoading: boolean;
  agentsIsLoading: boolean;
  virtualNosIsLoading: boolean;
  VNAssignmentIsLoading: boolean;
  integrationCountIsLoading: boolean;
  ivrCountIsLoading: boolean;
  MSleadIsLoading: boolean;
  MSProjectleadIsLoading: boolean;
  IVRList: any[];
  facebookMarketing: any[];
  isFacebookMarketingLoading: boolean;
  fbAccountIsLoading: boolean;
  fbAccount: any[];
  facebookAccountCampaigns: any[];
  isFacebookAccountCampaignsLoading: boolean;
  facebookCampaignMarketing: any[];
  isFacebookCampaignMarketingLoading: boolean;
  // Google Campaign state
  googleCampaignAccounts: any[];
  googleCampaignAccountsIsLoading: boolean;
  googleCampaignMarketing: any[];
  isGoogleCampaignMarketingLoading: boolean;
  // Google Ads state
  googleAdsAccountAds: any[];
  // data: any;
  PageSize: number | null;
  PageNumber: number | null;
};

const initialState: IntegrationState = {
  integrationData: [],
  activePropertyId: '',
  integrationDetail: [],
  integrationGoogleDetail: [],
  integrationAccountData: '',
  integrationCount: [],
  ivrCount: {},
  subscribedforms: [],
  ivrIntegrationData: [],
  ivrDetailsById: [],
  commonIvrData: [],
  agents: [],
  accounts: [],
  addFBAcountResult: false,
  integrationGoogleLandingData: [],
  integrationGoogleLeadData: [],
  assignedUserIds: [],
  subscribedAds: [],
  agencyNameList: [],
  projects: [],
  accountForms: [],
  accountFormsIsLoading: true,
  integrationListIsLoading: true,
  googleIntegrationListIsLoading: true,
  virtualNos: [],
  VNAssignment: {},
  ivrServiceProviders: {},
  agencyNameListIsLoading: true,
  agentsIsLoading: true,
  virtualNosIsLoading: true,
  VNAssignmentIsLoading: true,
  integrationCountIsLoading: true,
  ivrCountIsLoading: true,
  MSleadIsLoading: true,
  MSProjectleadIsLoading: true,
  IVRList: [],
  // data: [],
  exportFacebookStatus: [],
  exportFacebookStatusLoading: true,
  webhookAccountsLoading: true,
  webhook: {},
  webhookAccount: {},
  doesAccountNameExists: false,
  PageSize: null,
  PageNumber: null,
  isFacebookMarketingLoading: true,
  facebookMarketing: [],
  fbAccountIsLoading: true,
  fbAccount: [],
  facebookAccountCampaigns: [],
  isFacebookAccountCampaignsLoading: false,
  facebookCampaignMarketing: [],
  isFacebookCampaignMarketingLoading: false,
  // Google Campaign initial state
  googleCampaignAccounts: [],
  googleCampaignAccountsIsLoading: true,
  googleCampaignMarketing: [],
  isGoogleCampaignMarketingLoading: true,
  // Google Ads initial state
  googleAdsAccountAds: [],
};

export function integrationReducer(
  state: IntegrationState = initialState,
  action: Action
): IntegrationState {
  switch (action.type) {
    case IntegrationActionTypes.ADD_INTEGRATION_SUCCESS:
      return {
        ...state,
        integrationData: (action as AddIntegrationSuccess).response,
      };
    case IntegrationActionTypes.INTEGRATION_EMAIL_SUCCESS:
      return {
        ...state,
        integrationData: (action as IntegrationEmailSuccess).response,
      };
    case IntegrationActionTypes.FETCH_INTEGRATION_LIST:
      return {
        ...state,
        integrationListIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_INTEGRATION_LIST_SUCCESS:
      return {
        ...state,
        integrationDetail: (action as FetchIntegrationListSuccess).integrationData,
        integrationListIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_LIST:
      return {
        ...state,
        integrationGoogleDetail: (action as FetchGoogleIntegrationListSuccess)
          .response,
        googleIntegrationListIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_LIST_SUCCESS:
      return {
        ...state,
        integrationGoogleDetail: (action as FetchGoogleIntegrationListSuccess)
          .response,
        googleIntegrationListIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_INTEGRATION_BY_ID_SUCCESS:
      return {
        ...state,
        integrationData: (action as FetchIntegrationByIdSuccess).response,
      };
    case IntegrationActionTypes.FETCH_IVR_DETAILS_BY_ID_SUCCESS:
      return {
        ...state,
        ivrDetailsById: (action as FetchIvrDetailsByIdSuccess).response,
      };
    case IntegrationActionTypes.FETCH_INTEGRATION_COUNT:
      return {
        ...state,
        integrationCountIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_INTEGRATION_COUNT_SUCCESS:
      return {
        ...state,
        integrationCount: (action as FetchIntegrationCountSuccess).response,
        integrationCountIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_IVR_INTEGRATION_COUNT:
      return {
        ...state,
        ivrCountIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_IVR_INTEGRATION_COUNT_SUCCESS:
      return {
        ...state,
        ivrCount: (action as FetchIVRIntegrationCountSuccess).response,
        ivrCountIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_SUBSCRIBED_FORMS_SUCCESS:
      return {
        ...state,
        subscribedforms: (action as FetchSubscribedFormsSuccess).response,
      };
    case IntegrationActionTypes.IVR_INTEGRATION_SUCCESS:
      return {
        ...state,
        ivrIntegrationData: (action as IvrIntegrationSuccess).response,
      };
    case IntegrationActionTypes.COMMON_IVR_INTEGRATION_SUCCESS:
      return {
        ...state,
        commonIvrData: (action as CommonIvrIntegrationSuccess).response,
      };
    case IntegrationActionTypes.FETCH_AGENTS_LIST:
      return {
        ...state,
        agentsIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_AGENTS_LIST_SUCCESS:
      return {
        ...state,
        agents: (action as FetchAgentsSuccess).response,
        agentsIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_VIRTUAL_NOS:
      return {
        ...state,
        virtualNosIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_VIRTUAL_NOS_SUCCESS:
      return {
        ...state,
        virtualNos: (action as FetchVirtualNosSuccess).response,
        virtualNosIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_VIRTUAL_NO_ASSIGNMENT:
      return {
        ...state,
        VNAssignmentIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_VIRTUAL_NO_ASSIGNMENT_SUCCESS:
      return {
        ...state,
        VNAssignment: (action as FetchVNAssignmentSuccess).response,
        VNAssignmentIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_IVR_SERVICE_PROVIDERS_SUCCESS:
      return {
        ...state,
        ivrServiceProviders: (action as FetchIvrServiceProvidersSuccess).response,
      };
    case IntegrationActionTypes.FETCH_FACEBOOK_ACCOUNTS_SUCCESS:
      return {
        ...state,
        accounts: (action as FetchFacebookAccountsSuccess).response,
      };
    case IntegrationActionTypes.ADD_FACEBOOK_ACCOUNT_SUCCESS:
      return {
        ...state,
        addFBAcountResult: (action as AddFacebookAccountSuccess).response,
      };
    case IntegrationActionTypes.GOOGLE_ADS_LANDING_PAGE_SUCCESS:
      return {
        ...state,
        integrationGoogleLandingData: (
          action as AddIntegrationGoogleLandingSuccess
        ).response,
      };
    case IntegrationActionTypes.GOOGLE_ADS_LEAD_FORM_SUCCESS:
      return {
        ...state,
        integrationGoogleLeadData: (action as AddIntegrationGoogleLeadSuccess)
          .response,
      };
    case IntegrationActionTypes.FETCH_INTEGRATION_ASSIGNMENT_DETAILS_SUCCESS:
      return {
        ...state,
        assignedUserIds: (action as FetchIntegrationAssignmentDetailsSuccess)
          .response,
      };
    case IntegrationActionTypes.TOGGLE_fb_SUBSCRIPTION_SUCCESS:
      return {
        ...state,
        subscribedAds: (action as ToggleFBSubscriptionSuccess).response,
      };
    case IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_BY_ID_SUCCESS:
      return {
        ...state,
        integrationData: (action as FetchIntegrationByIdSuccess).response,
      };
    case IntegrationActionTypes.FETCH_AGENCY_NAME_LIST:
      return {
        ...state,
        agencyNameListIsLoading: true
      };
    case IntegrationActionTypes.FETCH_AGENCY_NAME_LIST_SUCCESS:
      return {
        ...state,
        agencyNameList: (action as FetchAgencyNameListSuccess).response,
        agencyNameListIsLoading: false
      };
    case IntegrationActionTypes.FETCH_INTEGRATION_PROJECTS_SUCCESS:
      return {
        ...state,
        projects: (action as FetchIntegrationProjectsSuccess).response,
      };
    case IntegrationActionTypes.FETCH_FB_ACCOUNT_FORMS:
      return {
        ...state,
        accountFormsIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_FB_ACCOUNT_FORMS_SUCCESS:
      return {
        ...state,
        accountForms: (action as FetchFbAccountFormsSuccess).response,
        accountFormsIsLoading: false,
      };
    // case IntegrationActionTypes.AGENCY_NAME_SUCCESS:
    //   return {
    //     ...state,
    //     data: (action as AgencyNameSuccess).response,
    //   };
    case IntegrationActionTypes.ADD_MS_LEAD:
      return {
        ...state,
        MSleadIsLoading: true
      };
    case IntegrationActionTypes.ADD_MS_LEAD_SUCCESS:
      return {
        ...state,
        MSleadIsLoading: false
      };
    case IntegrationActionTypes.ADD_PROJECT_MS_LEAD:
      return {
        ...state,
        MSProjectleadIsLoading: true
      };
    case IntegrationActionTypes.ADD_PROJECT_MS_LEAD_SUCCESS:
      return {
        ...state,
        MSProjectleadIsLoading: false
      };
    case IntegrationActionTypes.FETCH_IVR_LIST_SUCCESS:
      return {
        ...state,
        IVRList: (action as FetchIVRListSuccess).resp
      };
    case IntegrationActionTypes.FETCH_EXPORT_FACEBOOK_STATUS:
      return {
        ...state,
        exportFacebookStatusLoading: true,
      };
    case IntegrationActionTypes.FETCH_EXPORT_FACEBOOK_STATUS_SUCCESS:
      return {
        ...state,
        exportFacebookStatus: (action as FetchExportFacebookStatusSuccess)
          .response,
        exportFacebookStatusLoading: false,
      };
    case IntegrationActionTypes.FETCH_WEBHOOK_SUCCESS:
      return {
        ...state,
        webhook: (action as FetchWebhookSuccess).resp
      };
    case IntegrationActionTypes.FETCH_WEBHOOK_ACCOUNT:
      return {
        ...state,
        webhookAccountsLoading: true,
      };
    case IntegrationActionTypes.FETCH_WEBHOOK_ACCOUNT_SUCCESS:
      return {
        ...state,
        webhookAccount: (action as FetchWebhookAccountSuccess).response.data,
        webhookAccountsLoading: false,
      };
    case IntegrationActionTypes.DOES_ACCOUNTNAME_EXISTS_SUCCESS:
      return {
        ...state,
        doesAccountNameExists: (action as DoesExistAccountNameSuccess).response,
      };
    case IntegrationActionTypes.ASSIGN_PAGE_SIZE_NUMBER:
      return {
        ...state,
        PageSize: (action as AssignPageSize).PageSize,
        PageNumber: (action as AssignPageSize).PageNumber,
      };
    case IntegrationActionTypes.FETCH_FACEBOOK_MARKETING:
      return {
        ...state,
        isFacebookMarketingLoading: true,
      };
    case IntegrationActionTypes.FETCH_FACEBOOK_MARKETING_SUCCESS:
      return {
        ...state,
        facebookMarketing: (action as FetchFacebookMarketingSuccess).response,
        isFacebookMarketingLoading: false,
      };
    case IntegrationActionTypes.FETCH_FB_ACCOUNT:
      return {
        ...state,
        fbAccountIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_FB_ACCOUNT_SUCCESS:
      return {
        ...state,
        fbAccount: (action as FetchFbAccountSuccess).response,
        fbAccountIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_FACEBOOK_ACCOUNT_CAMPAIGNS:
      return {
        ...state,
        isFacebookAccountCampaignsLoading: true,
      };
    case IntegrationActionTypes.FETCH_FACEBOOK_ACCOUNT_CAMPAIGNS_SUCCESS:
      return {
        ...state,
        facebookAccountCampaigns: (action as FetchFacebookAccountCampaignsSuccess).response,
        isFacebookAccountCampaignsLoading: false,
      };
    case IntegrationActionTypes.FETCH_FACEBOOK_CAMPAIGN_MARKETING:
      return {
        ...state,
        isFacebookCampaignMarketingLoading: true,
      };
    case IntegrationActionTypes.FETCH_FACEBOOK_CAMPAIGN_MARKETING_SUCCESS:
      return {
        ...state,
        facebookCampaignMarketing: (action as FetchFacebookCampaignMarketingSuccess).response,
        isFacebookCampaignMarketingLoading: false,
      };
    // Google Campaign cases
    case IntegrationActionTypes.FETCH_GOOGLE_CAMPAIGN_ACCOUNTS:
      return {
        ...state,
        googleCampaignAccountsIsLoading: true,
      };
    case IntegrationActionTypes.FETCH_GOOGLE_CAMPAIGN_ACCOUNTS_SUCCESS:
      return {
        ...state,
        googleCampaignAccounts: (action as any).response,
        googleCampaignAccountsIsLoading: false,
      };
    case IntegrationActionTypes.FETCH_GOOGLE_CAMPAIGN_MARKETING:
      return {
        ...state,
        isGoogleCampaignMarketingLoading: true,
      };
    case IntegrationActionTypes.FETCH_GOOGLE_CAMPAIGN_MARKETING_SUCCESS:
      return {
        ...state,
        googleCampaignMarketing: (action as any).response,
        isGoogleCampaignMarketingLoading: false,
      };
    case IntegrationActionTypes.DELETE_GOOGLE_CAMPAIGN_ACCOUNT:
      return {
        ...state,
        googleCampaignAccountsIsLoading: true,
      };
    case IntegrationActionTypes.DELETE_GOOGLE_CAMPAIGN_ACCOUNT_SUCCESS:
      return {
        ...state,
        googleCampaignAccountsIsLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.integrationData;

export const getIntegrationDetails = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationDetail
);

export const getIntegrationDetailsIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationListIsLoading
);

export const getGoogleIntegrationDetails = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationGoogleDetail
);

export const getGoogleIntegrationIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.googleIntegrationListIsLoading
);

export const getExcelFileLink = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationData
);

export const getIvrDetailsById = createSelector(
  selectFeature,
  (state: IntegrationState) => state.ivrDetailsById
);

export const getGoogleLandingExcelFileLink = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationGoogleLandingData
);

export const getGoogleLeadExcelFileLink = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationGoogleLeadData
);

export const getIntegrationAccountExcel = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationAccountData
);

export const getSubscribedForms = createSelector(
  selectFeature,
  (state: IntegrationState) => state.subscribedforms
);

export const getExcelFileIvrLink = createSelector(
  selectFeature,
  (state: IntegrationState) => state.ivrIntegrationData
);

export const getCommonIvrExcelLink = createSelector(
  selectFeature,
  (state: IntegrationState) => state.commonIvrData
);

export const getAgents = createSelector(
  selectFeature,
  (state: IntegrationState) => state.agents
);

export const getVirtualNos = createSelector(
  selectFeature,
  (state: IntegrationState) => state.virtualNos
);

export const getVNAssignment = createSelector(
  selectFeature,
  (state: IntegrationState) => state.VNAssignment
);

export const getIvrServiceProviders = createSelector(
  selectFeature,
  (state: IntegrationState) => state.ivrServiceProviders
);

export const fetchFacebookAccounts = createSelector(
  selectFeature,
  (state: IntegrationState) => state.accounts
);

export const addFacebookAccount = createSelector(
  selectFeature,
  (state: IntegrationState) => state.addFBAcountResult
);

export const getAssignmentDetails = createSelector(
  selectFeature,
  (state: IntegrationState) => state.assignedUserIds
);

export const getSubscribedAds = createSelector(
  selectFeature,
  (state: IntegrationState) => state.subscribedAds
);

export const getAgencyNameList = createSelector(
  selectFeature,
  (state: IntegrationState) => state.agencyNameList
);

export const getAgencyNameListIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.agencyNameListIsLoading
);

export const getIntegrationProjects = createSelector(
  selectFeature,
  (state: IntegrationState) => state.projects
);

export const fetchFbAccountForms = createSelector(
  selectFeature,
  (state: IntegrationState) => state.accountForms
);

export const fetchFbAccountFormsIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.accountFormsIsLoading
);

export const getAgentsIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.agentsIsLoading
);

export const getVirtualNosIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.virtualNosIsLoading
);

export const getVNAssignmentIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.VNAssignmentIsLoading
);

export const getIntegrationCount = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationCount
);

export const getIntegrationCountIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.integrationCountIsLoading
);

export const getIVRCount = createSelector(
  selectFeature,
  (state: IntegrationState) => state.ivrCount
);

export const getIVRCountIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.ivrCountIsLoading
);

export const getMSleadIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.MSleadIsLoading
);

export const getProjectMSleadIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.MSProjectleadIsLoading
);

export const getIVRList = createSelector(
  selectFeature,
  (state: IntegrationState) => state.IVRList
);

export const getExportFacebookStatus = createSelector(
  selectFeature,
  (state: IntegrationState) => state.exportFacebookStatus
);

export const getExportFacebookStatusLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.exportFacebookStatusLoading
);

export const getWebhook = createSelector(
  selectFeature,
  (state: IntegrationState) => state.webhook
);

export const getWebhookAccount = createSelector(
  selectFeature,
  (state: IntegrationState) => state.webhookAccount
);

export const getWebhookAccountIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.webhookAccountsLoading
);

export const doesAccountNameExists = createSelector(
  selectFeature,
  (state: IntegrationState) => state.doesAccountNameExists
);

export const getFacebookMarketing = createSelector(
  selectFeature,
  (state: IntegrationState) => state.facebookMarketing
);

export const getFacebookMarketingIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.isFacebookMarketingLoading
);

export const getFbAccount = createSelector(
  selectFeature,
  (state: IntegrationState) => state.fbAccount
);

export const getFbAccountIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.fbAccountIsLoading
);

export const getFacebookAccountCampaigns = createSelector(
  selectFeature,
  (state: IntegrationState) => state.facebookAccountCampaigns
);

export const getFacebookAccountCampaignsIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.isFacebookAccountCampaignsLoading
);

export const getFacebookCampaignMarketing = createSelector(
  selectFeature,
  (state: IntegrationState) => state.facebookCampaignMarketing
);

export const getFacebookCampaignMarketingIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.isFacebookCampaignMarketingLoading
);
// Google Campaign selectors
export const getGoogleCampaignAccounts = createSelector(
  selectFeature,
  (state: IntegrationState) => state.googleCampaignAccounts
);

export const getGoogleCampaignAccountsIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.googleCampaignAccountsIsLoading
);

export const getGoogleCampaignMarketing = createSelector(
  selectFeature,
  (state: IntegrationState) => state.googleCampaignMarketing
);

export const getGoogleCampaignMarketingIsLoading = createSelector(
  selectFeature,
  (state: IntegrationState) => state.isGoogleCampaignMarketingLoading
);

// Google Ads selectors
export const getGoogleAdsAccountAds = createSelector(
  selectFeature,
  (state: IntegrationState) => state.googleAdsAccountAds
);
