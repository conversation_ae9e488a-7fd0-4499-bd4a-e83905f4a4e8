import { Injectable } from '@angular/core';
import { Observable, from, of, shareReplay } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';
import { CommonService } from '../shared/common.service';
import { MasterDataService } from '../controllers/master-data.service';

@Injectable({
  providedIn: 'root',
})
export class ManageSourceIndexDBService {
  private lastModifiedDatesUrl: any;
  private readonly DB_NAME = 'sourcesDB';
  private readonly DB_VERSION = 1;
  private readonly SOURCES_STORE = 'sources';
  private readonly SOURCE_LIST_KEY = 'sourceList';
  private readonly LAST_MODIFIED_KEY = 'lastModifiedDate';
  private db: IDBDatabase | null = null;

  constructor(
    private http: HttpClient,
    private commonService: CommonService,
    private masterDataService: MasterDataService
  ) {
    this.lastModifiedDatesUrl = this.masterDataService.fetchModifiedDate();
    this.initDB();
  }

  // Initialize the IndexedDB
  private initDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      if (this.db) {
        resolve(this.db);
        return;
      }
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);
      request.onupgradeneeded = (event: any) => {
        const db = event.target.result as IDBDatabase;
        if (!db.objectStoreNames.contains(this.SOURCES_STORE)) {
          db.createObjectStore(this.SOURCES_STORE, { keyPath: 'key' });
        }
      };
      request.onerror = (event: any) => {
        console.error('Error opening database:', event.target.error);
        reject(event.target.error);
      };
      request.onsuccess = (event: any) => {
        this.db = event.target.result;
        resolve(this.db);
      };
    });
  }
  private lastModifiedDatesCache$: Observable<any> | null = null;
  getLastModifiedDatesFromAPI(): Observable<any> {
    // Return cached response if available
    if (this.lastModifiedDatesCache$) {
      return this.lastModifiedDatesCache$;
    }
    this.lastModifiedDatesCache$ = this.http.get(this.lastModifiedDatesUrl).pipe(
      shareReplay(1),
      catchError(error => {
        console.error('Error fetching last modified dates:', error);
        this.lastModifiedDatesCache$ = null; // Clear cache on error
        return of(null);
      })
    );

    return this.lastModifiedDatesCache$;
  }

  // Check if sources need to be updated
  checkSourcesNeedUpdate(): Observable<boolean> {
    // First check if we have sources in IndexedDB
    return from(this.getLastModifiedDateFromIndexDB()).pipe(
      switchMap((dbDate: string | null) => {
        // If no date in DB, we definitely need to update
        if (!dbDate) {
          return of(true);
        }
        // We have a date in DB, now check if it's still current
        return this.getLastModifiedDatesFromAPI().pipe(
          map((response: any): boolean => {
            if (!response || !response.succeeded) {
              return false; // Use cached data if API fails
            }
            const apiSourcesDate = response?.data?.Source;
            if (!apiSourcesDate) {
              return false; // No date in API response, use cached data
            }
            // Compare dates and return true if update is needed
            if (dbDate !== apiSourcesDate) {
              return true;
            }
            return false; // Dates match, no update needed
          }),
          catchError((error): Observable<boolean> => {
            console.error('Error comparing dates:', error);
            return of(false); // Use cached data on error
          })
        );
      }),
      catchError((error): Observable<boolean> => {
        console.error('Error in checkSourcesNeedUpdate:', error);
        return of(true); // Assume update needed on error
      })
    );
  }

  // Get the last modified date from IndexedDB
  private async getLastModifiedDateFromIndexDB(): Promise<string | null> {
    try {
      const db = await this.initDB();
      return new Promise<string | null>((resolve) => {
        try {
          const transaction = db.transaction(this.SOURCES_STORE, 'readonly');
          const store = transaction.objectStore(this.SOURCES_STORE);
          const request = store.get(this.LAST_MODIFIED_KEY);

          request.onerror = (event: any) => {
            console.error('Error getting last modified date from IndexDB:', event.target.error);
            resolve(null);
          };

          request.onsuccess = (event: any) => {
            const result = event.target.result;
            if (result) {
              resolve(result.value);
            } else {
              resolve(null);
            }
          };

          transaction.onerror = (event: any) => {
            console.error('Transaction error in getLastModifiedDateFromIndexDB:', event.target.error);
            resolve(null);
          };
        } catch (error) {
          console.error('Error in getLastModifiedDateFromIndexDB transaction:', error);
          resolve(null);
        }
      });
    } catch (error) {
      console.error('Failed to initialize DB in getLastModifiedDateFromIndexDB:', error);
      return null;
    }
  }

  // Store sources in IndexedDB along with lastModified date
  storeSources(sources: any[], lastModified?: string): Observable<boolean> {
    if (!sources) {
      console.warn('No sources to store in IndexedDB');
      return of(false);
    }
    // If lastModified is provided, store both sources and date
    // Otherwise, just store the sources
    return from(this.initDB()).pipe(
      switchMap(db => {
        return new Promise<boolean>((resolve) => {
          try {
            const transaction = db.transaction(this.SOURCES_STORE, 'readwrite');
            const store = transaction.objectStore(this.SOURCES_STORE);
            let success = true;
            // Store the source list
            const sourceData = { key: this.SOURCE_LIST_KEY, value: sources };
            const sourceRequest = store.put(sourceData);
            sourceRequest.onerror = (event: any) => {
              console.error('Error storing source list:', event.target.error);
              success = false;
            };
            // If lastModified is provided, store it too
            if (lastModified) {
              const dateData = { key: this.LAST_MODIFIED_KEY, value: lastModified };
              const dateRequest = store.put(dateData);
              dateRequest.onerror = (event: any) => {
                console.error('Error storing last modified date:', event.target.error);
                success = false;
              };

              dateRequest.onsuccess = () => {
              };
            }
            sourceRequest.onsuccess = () => {
            };
            transaction.oncomplete = () => {
              resolve(success);
            };
            transaction.onerror = (event: any) => {
              console.error('Sources storage transaction error:', event.target.error);
              resolve(false);
            };
          } catch (error) {
            console.error('Error in storeSources transaction:', error);
            resolve(false);
          }
        });
      }),
      catchError(error => {
        console.error('Error storing sources:', error);
        return of(false);
      })
    );
  }

  // Get sources from IndexedDB
  getSources(): Observable<any[]> {
    return from(this.getSourceList()).pipe(
      tap(sources => {
      }),
      catchError(error => {
        console.error('Error getting sources from IndexedDB:', error);
        return of([]);
      })
    );
  }

  // Get the source list from IndexedDB
  private async getSourceList(): Promise<any[]> {
    try {
      const db = await this.initDB();
      return new Promise<any[]>((resolve) => {
        try {
          const transaction = db.transaction(this.SOURCES_STORE, 'readonly');
          const store = transaction.objectStore(this.SOURCES_STORE);
          const request = store.get(this.SOURCE_LIST_KEY);
          request.onerror = (event: any) => {
            console.error('Error getting source list:', event.target.error);
            resolve([]);
          };

          request.onsuccess = (event: any) => {
            const result = event.target.result;
            if (result && Array.isArray(result.value)) {
              resolve(result.value);
            } else {
              resolve([]);
            }
          };
          transaction.onerror = (event: any) => {
            console.error('Transaction error in getSourceList:', event.target.error);
            resolve([]);
          };
        } catch (error) {
          console.error('Error in getSourceList transaction:', error);
          resolve([]);
        }
      });
    } catch (error) {
      console.error('Failed to initialize DB in getSourceList:', error);
      return [];
    }
  }

  // Clear all caches - call this when you need to force a refresh
  clearCaches(): void {
    this.lastModifiedDatesCache$ = null;
    this.sourcesCache$ = null;
  }

  // Clear IndexedDB completely - call this on logout
  clearIndexedDB(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        // Close the current database connection if open
        if (this.db) {
          this.db.close();
          this.db = null;
        }
        this.clearCaches();
        // Delete the entire database
        const deleteRequest = indexedDB.deleteDatabase(this.DB_NAME);
        deleteRequest.onsuccess = () => {
          resolve(true);
        };
        deleteRequest.onerror = (event: any) => {
          console.error('Error clearing source IndexedDB:', event.target.error);
          resolve(false);
        };
        deleteRequest.onblocked = () => {
          console.warn('Source IndexedDB deletion blocked');
          resolve(false);
        };
      } catch (error) {
        console.error('Error in clearIndexedDB:', error);
        resolve(false);
      }
    });
  }

  forceRefreshSources(): Observable<any[]> {
    this.clearCaches();
    return this.fetchAndStoreSourcesFromAPI();
  }

  getSourcesWithCaching(): Observable<any[]> {
    if (this.sourcesCache$) {
      return this.sourcesCache$;
    }
    return this.checkSourcesNeedUpdate().pipe(
      switchMap(needsUpdate => {
        if (needsUpdate) {
          return this.fetchAndStoreSourcesFromAPI();
        } else {
          return this.getSources();
        }
      }),
      catchError(error => {
        console.error('Error in getSourcesWithCaching:', error);
        return this.getSources().pipe(
          switchMap(sources => {
            if (sources && sources.length > 0) {
              return of(sources);
            } else {
              return this.fetchAndStoreSourcesFromAPI();
            }
          })
        );
      })
    );
  }

  private sourcesCache$: Observable<any[]> | null = null;

  private fetchAndStoreSourcesFromAPI(): Observable<any[]> {
    if (this.sourcesCache$) {
      return this.sourcesCache$;
    }
    this.sourcesCache$ = this.getLastModifiedDatesFromAPI().pipe(
      switchMap((response: any) => {
        const lastModified = response?.succeeded && response?.data?.Source
          ? response.data.Source
          : new Date().toISOString();
        return this.commonService.getModuleListByAdvFilter({ path: 'source' }).pipe(
          switchMap((resp: any) => {
            const apiSources = resp?.data || [];
            if (apiSources.length === 0) {
              console.log('No sources found in API response');
              return of([]);
            }
            return this.storeSources(apiSources, lastModified).pipe(
              map(() => {
                // Clear the lastModifiedDatesCache to ensure fresh data on next call
                this.lastModifiedDatesCache$ = null;
                return apiSources;
              })
            );
          }),
          catchError((error) => {
            console.error('Error fetching sources from API:', error);
            return this.getSources().pipe(
              map(cachedSources => {
                if (cachedSources?.length > 0) {
                  return cachedSources;
                }
                return [];
              })
            );
          })
        );
      }),
      // Use shareReplay to avoid multiple subscriptions causing multiple API calls
      shareReplay(1),
      catchError((error) => {
        console.error('Error in fetchAndStoreSourcesFromAPI:', error);
        this.sourcesCache$ = null; // Clear cache on error
        return of([]);
      })
    );

    return this.sourcesCache$;
  }

}
