<div class="border-white mt-20 bg-white br-2 w-100 reports">
    <div class="p-10 flex-between border-bottom">
        <h5 class="text-black-200">{{ "DASHBOARD.marketing" | translate }}</h5>
        <form class="d-flex position-relative" [formGroup]="cplTrackForm">
            <div class="align-center position-relative cursor-pointer d-flex mr-10">
                <span *ngIf="!cplTrackForm.controls['cplAccountName'].value"
                    class="position-absolute left-6 z-index-2 text-xs text-black-200">
                    Account Name</span>
                <div class="show-hide-slate w-90">
                    <ng-select [virtualScroll]="true" [items]="accountDetails" [multiple]="false" [searchable]="false"
                        [closeOnSelect]="true" [clearable]="false" ResizableDropdown bindLabel="facebookAccountName"
                        bindValue="accountId" (change)="onCplAccountChange()" formControlName="cplAccountName">
                    </ng-select>
                </div>
            </div>
            <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                (click)="onFilterClick('showCplFilter')">
                <span
                    class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4"></span>
                <span
                    class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{cplTrackForm.controls['cplTrackRange'].value}}</span>
                <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
            </div>
            <div class="position-absolute top-30 w-270 bg-white right-0 z-index-1001"
                *ngIf="filters.showCplFilter">
                <div class="d-flex">
                    <div class="w-100 bg-light-pearl">
                        <div
                            class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                            Range
                        </div>
                        <ng-container *ngFor="let type of cplDateFilterList">
                            <div class="form-check form-check-inline p-6">
                                <input type="radio" id="inpcplTrack{{type.value}}"
                                    name="cplTrackRange" formControlName="cplTrackRange"
                                    [value]="type.value" class="radio-check-input w-8 h-8 mr-8">
                                <label class="text-dark-gray cursor-pointer text-large text-sm"
                                    for="inpcplTrack{{type.value}}">{{type.displayName}}</label>
                            </div>
                        </ng-container>
                        <div class="position-relative dashboard-filter form-group m-6 mb-16"
                            [ngClass]="{'pe-none disabled' : cplTrackForm.controls['cplTrackRange'].value !== 'Custom'}">
                            <form-errors-wrapper
                                [control]="cplTrackForm.controls['cplTrackDate']"
                                label="Date">
                                <input type="text" readonly [owlDateTimeTrigger]="dt1"
                                    [owlDateTime]="dt1" [selectMode]="'range'"
                                    formControlName="cplTrackDate" placeholder="Select date" />
                                <owl-date-time [pickerType]="'calendar'" #dt1
                                    (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
                <div class="flex-end p-6">
                    <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                        (click)="filters.showCplFilter = false">
                        Cancel</h6>
                    <div class="btn-coal" (click)="onCplTrackChange()">Apply</div>
                </div>
            </div>
        </form>
    </div>
    <div class="align-center bg-white w-100 px-10 no-validation border-bottom">
        <span class="search icon ic-search ic-sm ic-slate-90 mr-12"> </span>
        <input placeholder="type agent name" name="search" class="border-0 outline-0 w-100 py-12" autocomplete="off"
            [(ngModel)]="searchTerm" (keyup.enter)="search($event)" (input)="clearSearch()">
    </div>
    <ng-container *ngIf="!isCallLoading else spinLoader">
        <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize"
            [gridOptions]="gridOptions" [rowData]="rowData" [alwaysShowHorizontalScroll]="true"
            [alwaysShowVerticalScroll]="true" [suppressPaginationPanel]="true"
            (gridReady)="onGridReady($event)"></ag-grid-angular>
        <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4"
            *ngIf="totalCount > 0 && filtersPayload?.LeadVisibility">
            <div class="mr-10 ip-mt-10">Showing {{(offset * pageSize) + 1}} to
                {{(offset * pageSize) + pageSize > totalCount ? totalCount : (offset * pageSize) + pageSize}} of
                {{totalCount}} entries</div>
            <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
                <div class="flex-center">Entries per page
                    <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" [searchable]="false"
                        ResizableDropdown class="w-80" (change)="assignCount()" [(ngModel)]="selectedPageSize">
                        <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                            {{pageSize}}</ng-option>
                    </ng-select>
                </div>
                <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
                <pagination [offset]="offset" [limit]="1" [range]="1" [size]="getPages(totalCount,pageSize)"
                    (pageChange)="onPageChange($event)" [isV2Pagination]="true">
                </pagination>
            </div>
        </div>
    </ng-container>
</div>
<ng-template #spinLoader>
    <div class="spin-loader my-20"></div>
</ng-template>
<ng-template #noDataFound>
    <tr>
        <td class="h-100 header-4 text-secondary">No Data Available for Selected Criteria. Please try again with
            Different Filter Options.</td>
    </tr>
</ng-template>