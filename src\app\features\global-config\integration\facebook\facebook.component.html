<div routerLink="/global-config" [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
  class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<ng-container *ngIf="canView && !isAccountsLoading else loader">
  <div class="w-100 p-30 h-100-46">
    <div class="flex-end  tb-flex-center-unset tb-flex-col">
      <!-- <div class="flex-grow-1 w-50 ip-w-100">
        <div class="align-center no-validation px-10 py-8 border-gray br-4 mr-12 bg-white"
          *ngIf="filteredAccounts?.length">
          <span class="search icon ic-search ic-sm ic-slate-90 mr-12"></span>
          <input type="text" placeholder="Search" [(ngModel)]="searchText" (ngModelChange)="filterTable()"
            class="border-0 outline-0 w-100" />
        </div>
      </div> -->
      <div class="flex-end flex-wrap w-50 tb-w-100">
        <ng-container *ngIf="filteredAccounts?.length">
          <a class="bg-coal p-8 br-6 mr-12 text-sm text-white tb-mt-10" (click)="openFacebookTracker()">
            <span class="ic-tracker icon ic-xxs"></span>
            <span class="ml-8 tb-d-none">Bulk Fetch Tracker</span>
          </a>
          <a class="bg-coal p-8 br-6 mr-12 text-sm text-white tb-mt-10" (click)="fetchFbBulkLeads()">
            {{ 'INTEGRATION.facebook-bulk-lead-fetch' | translate }}</a>
        </ng-container>
        <a class="bg-coal p-8 br-6 mr-12 text-sm text-white tb-mt-10" *ngIf="canAdd" (click)="openCustomFB()">Custom</a>
        <button *ngIf="canAdd" class="btn btn-xs btn-fb px-10 flex-center tb-mt-10" id="login" data-automate-id="login"
          (click)="login()"> <img src="../../../../assets/images/integration/fb.svg" width="20" height="20" alt=""><span
            class="ml-4">{{ 'INTEGRATION.login-facebook' | translate }}</span>
        </button>
      </div>
    </div>
    <div class="mt-30">
      <ng-container *ngIf="!filteredAccounts?.length else addAndListingPage">
        <div class="flex-center">
          <div class="w-100 m-20 min-w-350 max-w-350 ph-modal-unset">
            <div class="mt-40 justify-center-col">
              <div class="d-flex">
                <div class="align-center-col">
                  <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                    <span class="icon ic-download ic-lg ic-coal"></span>
                  </div>
                  <div class="border-left-dotted h-60"></div>
                </div>
                <p class="text-coal fw-600 ml-20 mt-20">{{ 'INTEGRATION.download-excel' | translate }}/ Click
                  {{ 'INTEGRATION.login-facebook' | translate }} </p>
              </div>
              <div class="d-flex">
                <div class="align-center-col">
                  <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                    <span class="icon ic-share ic-lg ic-coal"></span>
                  </div>
                  <div class="border-left-dotted h-60"></div>
                </div>
                <div class="text-gray ml-20 fw-semi-bold">{{ 'INTEGRATION.share-message1' | translate }}
                  <span class="fw-600 text-coal"> Facebook</span>
                  {{ 'INTEGRATION.share-message2' | translate }}
                  <span class="fw-600 text-coal"> Facebook</span>
                  {{ 'INTEGRATION.share-message3' | translate }} {{getAppName()}}
                  {{'INTEGRATION.share-message4' | translate }}
                </div>
              </div>
              <div class="d-flex">
                <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                  <span class="icon ic-connection ic-lg ic-coal"></span>
                </div>
                <div class="text-gray ml-20">
                  <p>{{ 'INTEGRATION.connection-message' | translate }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #addAndListingPage>
        <ng-container *ngFor="let account of filteredAccounts">
          <div class="align-center border-bottom bg-white p-8 fw-600 scrollbar scroll-hide ip-w-100-40">
            <div class="flex-grow-1 mr-10">
              <div class="text-xs fv-sm-caps mb-10">Account Name</div>
              <h5 class="text-light-gray fw-600 text-truncate mt-4">{{ account.facebookAccountName }}</h5>
            </div>
            <div class="w-150 mr-10">
              <div class="text-xs fv-sm-caps mb-10">{{ 'INTEGRATION.account-id' | translate }}</div>
              <h5 class="text-light-gray fw-600 text-truncate mt-4">{{ account.facebookUserId }}</h5>
            </div>
            <div class="w-60 mr-20">
              <div class="text-xs fv-sm-caps mb-10 text-nowrap">{{ 'INTEGRATION.leads-count' | translate }}</div>
              <h5 class="text-light-gray fw-600 text-truncate mt-4">{{account?.totalLeadCount}} ({{ account.leadCount
                }})</h5>
            </div>
            <div class="w-110 mr-30">
              <div class="text-xs fv-sm-caps mb-6">{{ 'GLOBAL.actions' | translate }}</div>
              <div class="align-center">
                <div *ngIf="canDelete" title="Delete" class="bg-light-red icon-badge"
                  (click)="initDeleteIntegration(account.id, account.facebookAccountName)">
                  <span class="icon ic-delete ic-xxxs"></span>
                </div>
                <div title="Sync" class="icon-badge bg-dark-orange" (click)="syncAds(account.id)">
                  <span class="icon ic-update ic-xxxs"></span>
                </div>
                <ng-container *ngIf="canAssign">
                  <div title="Assign To" class="bg-blue-800 icon-badge"
                    (click)="openAssignmentModal(assignmentModal, account);">
                    <span class="icon ic-assign-to ic-xxxs"></span>
                  </div>
                </ng-container>
                <ng-container>
                  <div [title]="account.isAllSubscribed ? 'Unsubscribe All' : 'Subscribe All'"
                    (click)="toggleAccountSubscription(account)" class="icon-badge"
                    [ngClass]="account.isAllSubscribed ? 'bg-light-red' : 'bg-accent-green'">
                    <span [ngClass]="account.isAllSubscribed ? 'ic-unsubscribe' : 'ic-subscribe'"
                      class="icon ic-xxxs"></span>
                  </div>
                </ng-container>
                <div *ngIf="globalSettings?.enableFacebookConversion" title="Pixel" class="bg-dark-blue-500 icon-badge"
                  (click)="openPixelModal(pixelModal, account)">
                  <span class="icon ic-html-code ic-xxs"></span>
                </div>
              </div>
            </div>
            <div class="d-flex align-center flex-wrap w-220">
              <div class="flex-center cursor-pointer mr-12" (click)="toggleExpand(account, 'isAdsExpanded')">
                <span [ngClass]="{'text-accent-green fw-700 header-5' : account.isAdsExpanded}">
                  {{'INTEGRATION.ads' | translate}}</span>
                <span [ngClass]="{ 'rotate-180 ic-accent-green' : account.isAdsExpanded}"
                  class="icon ic-triangle-down ic-xxs ic-coal ml-8"></span>
              </div>
              <div class="flex-center cursor-pointer mr-12" (click)="toggleExpand(account, 'isFormsExpanded')">
                <span [ngClass]="{'text-accent-green fw-700 header-5' : account.isFormsExpanded}">
                  {{'INTEGRATION.forms' | translate}}</span>
                <span [ngClass]="{ 'rotate-180 ic-accent-green' : account.isFormsExpanded}"
                  class="icon ic-triangle-down ic-xxs ic-coal ml-8"></span>
              </div>
              <div class="flex-center cursor-pointer" (click)="toggleExpand(account, 'isCampaignsExpanded')">
                <span [ngClass]="{'text-accent-green fw-700 header-5' : account.isCampaignsExpanded}">
                  Campaigns</span>
                <span [ngClass]="{ 'rotate-180 ic-accent-green' : account.isCampaignsExpanded}"
                  class="icon ic-triangle-down ic-xxs ic-coal ml-8"></span>
              </div>
            </div>
          </div>
          <div *ngIf="account.isAdsExpanded" class="bg-white w-100">
            <div class="flex-between border w-100">
              <div class="no-validation w-100 border-end position-relative mr-12">
                <div class="align-center px-10 py-12">
                  <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                  <input type="text" (input)="isEmptyInput()" placeholder="type to search" [(ngModel)]="searchTerm"
                    (keydown.enter)="onSearch()" [ngModelOptions]="{standalone: true}"
                    class="border-0 outline-0 w-100" />
                </div>
                <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
                  ({{ 'LEADS.lead-search-prompt' | translate }})</small>
              </div>
              <form class="d-flex mr-12 position-relative" [formGroup]="cplTrackForm">
                <div class="flex-center bg-violet-600 p-6 cursor-pointer" (click)="showCplFilter = !showCplFilter">
                  <span class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4"></span>
                  <span
                    class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{cplTrackForm.controls['cplTrackRange'].value}}</span>
                  <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                </div>
                <div class="position-absolute top-30 w-270 bg-white right-0 z-index-1001" *ngIf="showCplFilter">
                  <div class="d-flex">
                    <div class="w-100 bg-light-pearl">
                      <div class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                        Range
                      </div>
                      <ng-container *ngFor="let type of cplDateFilterList">
                        <div class="form-check form-check-inline p-6">
                          <input type="radio" id="inpcplTrack{{type.value}}" name="cplTrackRange"
                            formControlName="cplTrackRange" [value]="type.value" class="radio-check-input w-8 h-8 mr-8">
                          <label class="text-dark-gray cursor-pointer text-large text-sm"
                            for="inpcplTrack{{type.value}}">{{type.displayName}}</label>
                        </div>
                      </ng-container>
                      <div class="position-relative dashboard-filter form-group m-6 mb-16"
                        [ngClass]="{'pe-none disabled' : cplTrackForm.controls['cplTrackRange'].value !== 'Custom'}">
                        <form-errors-wrapper [control]="cplTrackForm.controls['cplTrackDate']" label="Date">
                          <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                            [selectMode]="'range'" formControlName="cplTrackDate" placeholder="Select date" />
                          <owl-date-time [pickerType]="'calendar'" #dt1
                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                        </form-errors-wrapper>
                      </div>
                    </div>
                  </div>
                  <div class="flex-end p-6">
                    <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                      (click)="showCplFilter = false">
                      Cancel</h6>
                    <div class="btn-coal" (click)="onCplTrackChange()">Apply</div>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <ng-container *ngIf="account.isAdsExpanded">
            <div *ngIf="account.isAdsLoading" class="flex-center p-20">
              <application-loader></application-loader>
            </div>
            <ng-container *ngIf="!account.isAdsLoading">
              <div *ngIf="account.paginatedAds?.length" class="manage-leads">
                <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"
                  [rowData]="account.paginatedAds" [suppressPaginationPanel]="true" [alwaysShowHorizontalScroll]="true"
                  [alwaysShowVerticalScroll]="true" (filterChanged)="onFilterChanged($event)">
                </ag-grid-angular>
              </div>
              <div *ngIf="!account.paginatedAds?.length" class="flex-center-col h-100-337">
                <img src="assets/images/layered-cards.svg" alt="No Data Found" />
                <div class="header-3 fw-600 text-center">
                  {{ "PROFILE.no-data-found" | translate }}
                </div>
              </div>
              <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4"
                *ngIf="account.paginatedAds?.length && adsTotalCount > 0">
                <div class="mr-10 ip-mt-10">
                  Showing {{((adsPageNumber - 1) * adsPageSize) + 1}} to
                  {{((adsPageNumber - 1) * adsPageSize) + adsPageSize > adsTotalCount ? adsTotalCount :
                  ((adsPageNumber - 1) * adsPageSize) + adsPageSize}} of
                  {{adsTotalCount}} entries
                </div>
                <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
                  <div class="flex-center">
                    <span class="mr-10">Entries per page</span>
                    <ng-select [virtualScroll]="true" [placeholder]="adsPageSize" bindValue="id" [searchable]="false"
                      ResizableDropdown class="w-80" (change)="assignAdsCount(account)" [(ngModel)]="adsPageSize">
                      <ng-option name="showEntriesSize" *ngFor="let pageSize of adsShowEntriesSize" [value]="pageSize">
                        {{pageSize}}
                      </ng-option>
                    </ng-select>
                  </div>
                  <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
                  <pagination [offset]="adsPageNumber-1" [limit]="1" [range]="1"
                    [size]="getPages(adsTotalCount,adsPageSize)" (pageChange)="onAdsPageChange($event, account)"
                    [isV2Pagination]="true">
                  </pagination>
                </div>
              </div>
            </ng-container>
            <div class="justify-center" [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
              <div
                class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 tb-flex-col z-index-2">
                <div class="align-center tb-flex-col">
                  <div class="align-center tb-mb-10">
                    <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
                      Items {{ 'LEADS.selected' | translate}}
                    </div>
                  </div>
                </div>
                <div class="d-flex scrollbar max-w-100-260 tb-max-w-100-190 ip-max-w-100-70 scroll-hide">
                  <button (click)="openBulkProjectModal(account.facebookAccountName, account.isAdsExpanded)"
                    *ngIf="canBulkAssignment" class="btn-bulk">Bulk Project</button>
                  <button (click)="openBulkLocationModal(account.facebookAccountName, account.isAdsExpanded)"
                    *ngIf="canBulkAssignment" class="btn-bulk">Bulk Location</button>
                  <button (click)="openBulkCountryCodeModal(account.facebookAccountName, account.isAdsExpanded)"
                    *ngIf="canBulkAssignment" class="btn-bulk">Bulk Country Code</button>
                  <button (click)="openBulkAssignModal(acount?.facebookAccountName, account.isAdsExpanded)"
                    *ngIf="canBulkReassign" class="btn-bulk">Bulk Reassign </button>
                  <button (click)="openBulkAgencyModal(account.facebookAccountName, account.isAdsExpanded)"
                    *ngIf="canBulkAssignment" class="btn-bulk">Bulk Agency </button>
                </div>
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="account.isFormsExpanded">
            <div *ngIf="account.isFormsLoading" class="flex-center p-20">
              <application-loader></application-loader>
            </div>
            <div *ngIf="!account.isFormsLoading" class="bg-white w-100">
              <div class="flex-between border w-100">
                <div class="no-validation w-100 position-relative mr-12">
                  <div class="align-center px-10 py-12">
                    <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                    <input type="text" (input)="isEmptyFormsInput()" placeholder="type to search"
                      [(ngModel)]="formsSearchTerm" (keydown.enter)="onFormsSearch()"
                      [ngModelOptions]="{standalone: true}" class="border-0 outline-0 w-100" />
                  </div>
                  <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
                    ({{ 'LEADS.lead-search-prompt' | translate }})</small>
                </div>
              </div>
            </div>
            <div *ngIf="!account.isFormsLoading" class="scrollbar ip-w-100-40 mb-30">
              <div *ngIf="account.paginatedForms?.length" class="manage-leads">
                <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptionsExternal"
                  (gridReady)="onGridReady($event)" [rowData]="account.paginatedForms" [suppressPaginationPanel]="true"
                  [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
                  (filterChanged)="onFilterChanged($event)">
                </ag-grid-angular>
                <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4"
                  *ngIf="account.paginatedForms?.length && formsTotalCount > 0">
                  <div class="mr-10 ip-mt-10">
                    Showing {{((formsPageNumber - 1) * formsPageSize) + 1}} to
                    {{((formsPageNumber - 1) * formsPageSize) + formsPageSize > formsTotalCount ? formsTotalCount :
                    ((formsPageNumber - 1) * formsPageSize) + formsPageSize}} of
                    {{formsTotalCount}} entries
                  </div>
                  <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
                    <div class="flex-center">
                      <span class="mr-10">Entries per page</span>
                      <ng-select [virtualScroll]="true" [placeholder]="formsPageSize" bindValue="id"
                        [searchable]="false" ResizableDropdown class="w-80" (change)="assignFormsCount(account)"
                        [(ngModel)]="formsPageSize">
                        <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                          {{pageSize}}
                        </ng-option>
                      </ng-select>
                    </div>
                    <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
                    <pagination [offset]="formsPageNumber-1" [limit]="1" [range]="1"
                      [size]="getPages(formsTotalCount,formsPageSize)" (pageChange)="onFormsPageChange($event, account)"
                      [isV2Pagination]="true">
                    </pagination>
                  </div>
                </div>
                <div class="justify-center" [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
                  <div
                    class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 tb-flex-col z-index-2">
                    <div class="align-center tb-flex-col">
                      <div class="align-center tb-mb-10">
                        <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
                          Items {{ 'LEADS.selected' | translate}}
                        </div>
                      </div>
                    </div>
                    <div class="d-flex scrollbar max-w-100-260 tb-max-w-100-190 ip-max-w-100-70 scroll-hide">
                      <button (click)="openBulkProjectModal(account.facebookAccountName, account.isAdsExpanded)"
                        *ngIf="canBulkAssignment" class="btn-bulk">Bulk Project</button>
                      <button (click)="openBulkLocationModal(account.facebookAccountName, account.isAdsExpanded)"
                        *ngIf="canBulkAssignment" class="btn-bulk">Bulk Location</button>
                      <button (click)="openBulkCountryCodeModal(account.facebookAccountName, account.isAdsExpanded)"
                        *ngIf="canBulkAssignment" class="btn-bulk">Bulk Country Code</button>
                      <button class="btn-bulk" id="btnFormBulkFormAssign" data-automate-id="btnFormBulkAssign"
                        *ngIf="canBulkReassign"
                        (click)="openBulkAssignModal(account.facebookAccountName, account.isAdsExpanded)">
                        Bulk Reassign </button>
                      <button class="btn-bulk" id="btnFormBulkAgency" data-automate-id="btnFormBulkAgency"
                        *ngIf="canBulkAssignment"
                        (click)="openBulkAgencyModal(account.facebookAccountName, account.isAdsExpanded)">
                        Bulk Agency </button>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="!account.paginatedForms?.length" class="flex-center-col h-100-337">
                <img src="assets/images/layered-cards.svg" alt="No Data Found" />
                <div class="header-3 fw-600 text-center">
                  {{ "PROFILE.no-data-found" | translate }}
                </div>
              </div>
            </div>
          </ng-container>
          <div *ngIf="account.isCampaignsExpanded" class="bg-white w-100">
            <div class="flex-between border w-100">
              <div class="no-validation w-100 position-relative mr-12">
                <div class="align-center px-10 py-12">
                  <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                  <input type="text" (input)="isEmptyCampaignsInput()" placeholder="type to search"
                    [(ngModel)]="campaignsSearchTerm" (keydown.enter)="onCampaignsSearch()"
                    [ngModelOptions]="{standalone: true}" class="border-0 outline-0 w-100" />
                </div>
                <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
                  ({{ 'LEADS.lead-search-prompt' | translate }})</small>
              </div>
              <form class="d-flex mr-12 position-relative" [formGroup]="campaignDateForm">
                <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                  (click)="showCampaignDateFilter = !showCampaignDateFilter">
                  <span class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4"></span>
                  <span
                    class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{campaignDateForm.controls['campaignDateRange'].value}}</span>
                  <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                </div>
                <div class="position-absolute top-30 w-270 bg-white right-0 z-index-1001"
                  *ngIf="showCampaignDateFilter">
                  <div class="d-flex">
                    <div class="w-100 bg-light-pearl">
                      <div class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                        Range
                      </div>
                      <ng-container *ngFor="let type of campaignDateFilterList">
                        <div class="form-check form-check-inline p-6">
                          <input type="radio" id="inpCampaignDate{{type.value}}" name="campaignDateRange"
                            formControlName="campaignDateRange" [value]="type.value"
                            class="radio-check-input w-8 h-8 mr-8">
                          <label class="text-dark-gray cursor-pointer text-large text-sm"
                            for="inpCampaignDate{{type.value}}">{{type.displayName}}</label>
                        </div>
                      </ng-container>
                      <div class="position-relative dashboard-filter form-group m-6 mb-16"
                        [ngClass]="{'pe-none disabled' : campaignDateForm.controls['campaignDateRange'].value !== 'Custom'}">
                        <form-errors-wrapper [control]="campaignDateForm.controls['campaignDate']" label="Date">
                          <input type="text" readonly [owlDateTimeTrigger]="dt2" [owlDateTime]="dt2"
                            [selectMode]="'range'" formControlName="campaignDate" placeholder="Select date" />
                          <owl-date-time [pickerType]="'calendar'" #dt2
                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                        </form-errors-wrapper>
                      </div>
                    </div>
                  </div>
                  <div class="flex-end p-6">
                    <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                      (click)="showCampaignDateFilter = false">
                      Cancel</h6>
                    <div class="btn-coal" (click)="onCampaignDateChange()">Apply</div>
                  </div>
                </div>
              </form>
            </div>
            <div *ngIf="account.isCampaignsLoading" class="flex-center p-20">
              <application-loader></application-loader>
            </div>
            <ng-container *ngIf="!account.isCampaignsLoading">
              <div *ngIf="account.hasCampaigns" class="manage-leads">
                <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptionsCampaigns"
                  (gridReady)="onCampaignGridReady($event)" [rowData]="account.campaigns"
                  [suppressRowClickSelection]="true" [rowSelection]="'none'" [suppressCellSelection]="true"
                  [enableRangeSelection]="false" [suppressRowDeselection]="true" [suppressMultiRangeSelection]="true"
                  [enableCellTextSelection]="true" [rowHeight]="60">
                </ag-grid-angular>
              </div>
              <div *ngIf="!account.hasCampaigns" class="flex-center-col h-100-337">
                <img src="assets/images/layered-cards.svg" alt="No Data Found" />
                <div class="header-3 fw-600 text-center">
                  {{ "PROFILE.no-data-found" | translate }}
                </div>
              </div>
              <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4"
                *ngIf="account.campaigns?.length && campaignsTotalCount > 0">
                <div class="mr-10 ip-mt-10">
                  Showing {{((campaignsPageNumber - 1) * campaignsPageSize) + 1}} to
                  {{((campaignsPageNumber - 1) * campaignsPageSize) + campaignsPageSize > campaignsTotalCount ?
                  campaignsTotalCount :
                  ((campaignsPageNumber - 1) * campaignsPageSize) + campaignsPageSize}} of
                  {{campaignsTotalCount}} entries
                </div>
                <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
                  <div class="flex-center">
                    <span class="mr-10">Entries per page</span>
                    <ng-select [virtualScroll]="true" [placeholder]="campaignsPageSize" bindValue="id"
                      [searchable]="false" ResizableDropdown class="w-80" (change)="assignCampaignsCount(account)"
                      [(ngModel)]="campaignsPageSize">
                      <ng-option name="showEntriesSize" *ngFor="let pageSize of campaignsShowEntriesSize"
                        [value]="pageSize">
                        {{pageSize}}
                      </ng-option>
                    </ng-select>
                  </div>
                  <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
                  <pagination [offset]="campaignsPageNumber-1" [limit]="1" [range]="1"
                    [size]="getPages(campaignsTotalCount,campaignsPageSize)"
                    (pageChange)="onCampaignsPageChange($event, account)" [isV2Pagination]="true">
                  </pagination>
                </div>
              </div>
            </ng-container>

          </div>
          <ng-template #assignmentModal>
            <form class="h-100vh prevent-text-select">
              <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
                <h3 class="fw-semi-bold">{{ 'GLOBAL.lead'| translate}}
                  {{ 'LEADS.assignment' | translate }}</h3>
                <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
              </div>
              <div class="p-16 h-100-114 scrollbar">
                <integration-assignment [image]="image" [isBulkAssignModel]="isBulkAssignModel"
                  [selectedAccountName]="selectedAccountName" [selectedIntegrations]="selectedIntegrations"
                  [canAllowSecondaryUsers]="canAllowSecondaryUsers" [sameAsPrimaryUsers]="sameAsPrimaryUsers"
                  [assignedSecondaryUsers]="assignedSecondaryUsers" [assignedDuplicateUser]="assignedDuplicateUser"
                  [assignedPrimaryUsers]="assignedPrimaryUsers" [sameAsSelectedUsers]="sameAsSelectedUsers"
                  [assignedUser]="assignedUser" [assignedUserDetails]="assignedUserDetails"
                  [updatedIntegrationList]="updatedIntegrationList" [moduleId]="moduleId"
                  [canAssignToAny]="canAssignToAny" [allActiveUsers]="allActiveUsers" [activeUsers]="activeUsers"
                  [selectedAccountId]="selectedAccountId" [selectedCount]="selectedCount"
                  [canEnableAllowDuplicates]="canEnableAllowDuplicates"
                  [canEnableAllowSecondaryUsers]="canEnableAllowSecondaryUsers" [allUserList]="allUserList"
                  [userList]="userList" [isFbComponent]="true" [isAdAccount]="isAdAccount"
                  [isFormAccount]="isFormAccount" [selectedAdName]="selectedAdName"
                  (isShowAssignModalChanged)="closeModal()"></integration-assignment>
              </div>
            </form>
          </ng-template>
          <ng-template #pixelModal>
            <form class="h-100vh prevent-text-select">
              <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
                <h3 class="fw-semi-bold">Pixel Integration</h3>
                <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
              </div>
              <div class="p-16">
                <img [type]="'leadrat'" [appImage]="metaPixel" alt="img" />
                <form [formGroup]="pixelForm" class="prevent-text-select mt-20" autocomplete="off">
                  <div class="label-req">
                    Pixel ID
                  </div>
                  <form-errors-wrapper [control]="pixelForm?.controls?.['pixelId']" label="pixel ID">
                    <input type="text" formControlName="pixelId" placeholder="ex. 434567865445" />
                  </form-errors-wrapper>
                  <div class="label-req">
                    Access Token
                  </div>
                  <form-errors-wrapper [control]="pixelForm?.controls?.['accessToken']" label="access token">
                    <input type="text" formControlName="accessToken" placeholder="ex. 434567865445" />
                  </form-errors-wrapper>
                  <div class="label-req">
                    Status
                  </div>
                  <form-errors-wrapper [control]="pixelForm?.controls?.['status']" label="Status">
                    <ng-select [items]="isCustomStatusEnabled ? customStatusList : masterLeadStatus" ResizableDropdown
                      bindLabel="displayName" [multiple]="true" [closeOnSelect]="false" [clearSearchOnAdd]="true"
                      name="status" placeholder="Choose Status" class="ng-select-gray" formControlName="status">
                      <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected">
                          <span class="checkmark"></span><span class="text-truncate-1 break-all">
                            {{item.displayName}}</span>
                        </div>
                      </ng-template>
                    </ng-select>
                  </form-errors-wrapper>
                </form>
                <ng-container *ngIf="pixelForm?.controls?.['status']?.value?.length">
                  <div class="mt-20 fw-600 text-sm text-mud">Selected Status</div>
                  <div class="border-solid-bottom w-20 mt-4"></div>
                  <div class="mt-12 scrollbar h-100-460">
                    <div class="flex-between mb-12" *ngFor="let status of pixelForm?.controls?.['status']?.value">
                      <div class="align-center">
                        <div class="fw-semi-bold text-sm text-mud">{{ status?.displayName }}</div>
                      </div>
                      <div (click)="removeStatus(status)" class="cursor-pointer pr-4">
                        <span class="bg-light-red icon-badge ic-xxs">
                          <span class="icon ic-trash m-auto ic-xxxs"></span></span>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </form>
            <div *ngIf="pixelForm?.dirty && isSaveVisible" class="flex-end modal-footer bg-white box-shadow-3">
              <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer"
                (click)="modalService.hide()">{{
                'BUTTONS.cancel' | translate }}</h6>
              <button class="btn-coal ml-20" (click)="onSubmit()">{{ pixelId ? 'Update Details' : 'Save
                Details'}}</button>
            </div>
          </ng-template>
        </ng-container>

      </ng-template>
    </div>
  </div>
</ng-container>
<ng-template #loader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>