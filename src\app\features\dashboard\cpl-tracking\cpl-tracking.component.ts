import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { PAGE_SIZE, SHOW_ENTRIES, DATE_FILTER_LIST } from 'src/app/app.constants';
import { DateRange } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getPages, validateAllFormFields, onPickerOpened, getDateRange, setTimeZoneDate } from 'src/app/core/utils/common.util';
import { FetchCPLTracking, UpdateCPLFilterPayload } from 'src/app/reducers/dashboard/dashboard.actions';
import { getCplTrack, getCplTrackIsLoading, getFiltersPayloadV1 } from 'src/app/reducers/dashboard/dashboard.reducers';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getFbAccount } from 'src/app/reducers/Integration/integration.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

@Component({
  selector: 'cpl-tracking',
  templateUrl: './cpl-tracking.component.html',
})
export class CplTrackingComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() usersByDesignation: Array<any>;
  isCallLoading: boolean;
  public pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  offset: number = 0;
  gridOptions: any;
  gridApi: any;
  searchTerm: string;
  gridColumnApi: any;
  rowData: any = [];
  defaultColDef: any;
  totalCount: number;
  getPages = getPages;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: this.pageSize,
  };
  selectedPageSize: number;
  userBasicDetails: any;
  accountDetails: any;
  showCplAccount: boolean = false;
  cplTrackForm: FormGroup;
  globalSettings: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  cplDateFilterList: any[] = DATE_FILTER_LIST?.filter((item: any) => item?.value !== 'TillDate');
  filters: any = {
    showCplFilter: false,
  };

  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>, private fb: FormBuilder

  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
  }

  ngOnInit() {
    this.selectedPageSize = 10;
    this.cplTrackForm = this.fb.group({
      cplAccountName: [null],
      cplTrackRange: ['Today'],
      cplTrackDate: [null],
    });
    this.store
      .select(getCplTrackIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCallLoading = isLoading;
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        // Trigger initial load with date filter if account is already selected
        if (data?.timeZoneInfo && this.cplTrackForm.get('cplAccountName')?.value) {
          this.onCplTrackChange();
        }
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettings = data;
      })

    this.store
      .select(getCplTrack)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.totalCount = data?.totalCount;
        this.rowData = data?.items;
        if (this.rowData?.length) {
          this.initializeGridSettings();
        }
      });
    this.store
      .select(getFbAccount)
      .pipe(takeUntil(this.stopper))
      .subscribe((response: any) => {
        this.accountDetails = response?.filter((account: any) => account?.ads?.length)
          .map((account: any) => ({
            accountId: account.accountId,
            facebookAccountName: account.facebookAccountName
          })) || [];
        if (this.accountDetails.length > 0 && !this.cplTrackForm.get('cplAccountName')?.value) {
          const firstAccount = this.accountDetails[0] as any;
          this.cplTrackForm.patchValue({
            cplAccountName: firstAccount.accountId
          });
          if (this.userBasicDetails?.timeZoneInfo) {
            this.onCplTrackChange();
          } else {
            this.onCplAccountChange();
          }
        }
      });
    this.store
      .select(getFiltersPayloadV1)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: any) => {
        this.filtersPayload = filters.global;
        if (this.rowData && this.rowData?.length === 0 && this.offset > 0) {
          this.filtersPayload = {
            ...this.filtersPayload,
            pageNumber: this.offset,

          };
          this.offset = this.offset - 1;
        }
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'User Name',
        field: 'User Name',
        valueGetter: (params: any) => `${params.data.userName || '--'}`,
        cellRenderer: (params: any) => `<p>${params.value}</p>`,
      },
      {
        headerName: 'Facebook Leads',
        field: 'Facebook Leads',
        valueGetter: (params: any) => `${params.data.fbLeads || '--'}`,
        cellRenderer: (params: any) => `<p>${params.value}</p>`,
      },
      // {
      //   headerName: 'Google Leads',
      //   field: 'Google Leads',
      //   valueGetter: (params: any) => `${params.data.googleLeads || '--'}`,
      //   cellRenderer: (params: any) => `<p>${params.value}</p>`,
      // },
      {
        headerName: 'CPL Per User',
        field: 'CPL Per User',
        valueGetter: (params: any) => {
          const budget = params.data?.cplPerUser;
          return budget !== null && budget !== undefined && budget !== 0 ? this.globalSettings?.countries?.[0]?.defaultCurrency + ' ' + `${budget}` : '--';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'No. of meeting done',
        field: 'No. of meeting done',
        valueGetter: (params: any) => [
          params.data.noOfMeetingDone,
          params.data.meetingDoneUniqueCount,
        ],
        cellRenderer: (params: any) => `<p>${params.value[0] || '--'}</p>`
          + `<p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
          }<span></p>`,
      },
      {
        headerName: 'No. of site visit done',
        field: 'No. of site visit done',
        valueGetter: (params: any) => [
          params.data.noOfSiteVisitDone,
          params.data.siteVisitDoneUniqueCount,
        ],
        cellRenderer: (params: any) => `<p>${params.value[0] || '--'}</p>`
          + `<p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
          }<span></p>`,
      },
      {
        headerName: 'No. of invoice',
        field: 'No. of invoice',
        valueGetter: (params: any) => `${params.data.NoOfInvoiced || '--'}`,
        cellRenderer: (params: any) => `<p>${params.value}</p>`,
      },
      {
        headerName: 'Total Revenue',
        field: 'Total Revenue',
        valueGetter: (params: any) => {
          const budget = params.data?.totalRevenue;
          return budget !== null && budget !== undefined && budget !== 0 ? this.globalSettings?.countries?.[0]?.defaultCurrency + ' ' + `${budget}` : '--';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'ROI',
        field: 'ROI',
        valueGetter: (params: any) => {
          const roi = params.data?.roi;
          return roi !== null && roi !== undefined && roi !== 0 ? `${roi}%` : '--';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
    ];
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  getSelectedAccountName(): string {
    const selectedAccountId = this.cplTrackForm.controls['cplAccountName'].value;
    if (!selectedAccountId) {
      return 'Select Account';
    }
    const selectedAccount = this.accountDetails.find((account: any) => account.accountId === selectedAccountId);
    return (selectedAccount as any)?.facebookAccountName || 'Select Account';
  }

  onFilterClick(filterName: string) {
    this.filters[filterName] = !this.filters[filterName];
  }

  onCplTrackChange() {
    if (!this.cplTrackForm.valid) {
      validateAllFormFields(this.cplTrackForm);
      return;
    }

    let currentFilters: any = {};
    this.store.select(getFiltersPayloadV1).pipe(takeUntil(this.stopper)).subscribe((filters: any) => {
      currentFilters = filters?.cplTracker || {};
    }).unsubscribe();

    const selectedAccountId = this.cplTrackForm.value.cplAccountName;
    const dateRange = this.cplTrackForm.value.cplTrackRange;
    const customDate = this.cplTrackForm.value.cplTrackDate;

    let payload: any = {
      ...currentFilters,
      AccountId: selectedAccountId,
    };

    if (this.userBasicDetails?.timeZoneInfo?.timeZoneId && this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) {
      payload.TimeZoneId = this.userBasicDetails.timeZoneInfo.timeZoneId;
      payload.BaseUTcOffset = this.userBasicDetails.timeZoneInfo.baseUTcOffset;
    }

    if (dateRange && dateRange !== 'Custom') {
      const dateRangeResult = getDateRange(DateRange[dateRange as keyof typeof DateRange], this.currentDate);
      if (dateRangeResult && dateRangeResult.length === 2) {
        payload.FromDate = setTimeZoneDate(dateRangeResult[0], this.userBasicDetails?.timeZoneInfo);
        payload.ToDate = setTimeZoneDate(dateRangeResult[1], this.userBasicDetails?.timeZoneInfo);
      }
    } else if (dateRange === 'Custom' && customDate && customDate.length === 2) {
      payload.FromDate = setTimeZoneDate(customDate[0], this.userBasicDetails?.timeZoneInfo);
      payload.ToDate = setTimeZoneDate(customDate[1], this.userBasicDetails?.timeZoneInfo);
    }

    this.store.dispatch(new UpdateCPLFilterPayload(payload));
    this.store.dispatch(new FetchCPLTracking(payload));
    this.filters.showCplFilter = false;
  }

  onCplAccountChange() {
    if (!this.cplTrackForm.valid) {
      validateAllFormFields(this.cplTrackForm);
      return;
    }

    // Get current filter payload and merge with account filter
    let currentFilters: any = {};
    this.store.select(getFiltersPayloadV1).pipe(takeUntil(this.stopper)).subscribe((filters: any) => {
      currentFilters = filters?.cplTracker || {};
    }).unsubscribe();

    const selectedAccountId = this.cplTrackForm.value.cplAccountName;
    let payload: any = {
      ...currentFilters,
      AccountId: selectedAccountId,
    };

    this.applyDateFilterToPayload(payload);

    this.store.dispatch(new UpdateCPLFilterPayload(payload));
    this.store.dispatch(new FetchCPLTracking(payload));
    this.showCplAccount = false;
  }

  private applyDateFilterToPayload(payload: any) {
    const dateRange = this.cplTrackForm.value.cplTrackRange;
    const customDate = this.cplTrackForm.value.cplTrackDate;

    if (this.userBasicDetails?.timeZoneInfo?.timeZoneId && this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) {
      payload.TimeZoneId = this.userBasicDetails.timeZoneInfo.timeZoneId;
      payload.BaseUTcOffset = this.userBasicDetails.timeZoneInfo.baseUTcOffset;
    }

    if (dateRange && dateRange !== 'Custom') {
      const dateRangeResult = getDateRange(DateRange[dateRange as keyof typeof DateRange], this.currentDate);
      if (dateRangeResult && dateRangeResult.length === 2) {
        payload.FromDate = setTimeZoneDate(dateRangeResult[0], this.userBasicDetails?.timeZoneInfo);
        payload.ToDate = setTimeZoneDate(dateRangeResult[1], this.userBasicDetails?.timeZoneInfo);
      }
    } else if (dateRange === 'Custom' && customDate && customDate.length === 2) {
      payload.FromDate = setTimeZoneDate(customDate[0], this.userBasicDetails?.timeZoneInfo);
      payload.ToDate = setTimeZoneDate(customDate[1], this.userBasicDetails?.timeZoneInfo);
    }
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridOptions.api = params.api;
    params.api.sizeColumnsToFit();
  }

  onPageChange(e: any) {
    this.offset = e;
    let filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.pageSize,
      PageNumber: e + 1,
      AccountId: this.cplTrackForm.value.cplAccountName || (this.accountDetails.length > 0 ? (this.accountDetails[0] as any).accountId : null),
    };
    this.applyDateFilterToPayload(filtersPayload);
    this.store.dispatch(new UpdateCPLFilterPayload(filtersPayload));
    this.store.dispatch(new FetchCPLTracking(filtersPayload));
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    let filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.pageSize,
      PageNumber: 1,
      AccountId: this.cplTrackForm.value.cplAccountName || (this.accountDetails.length > 0 ? (this.accountDetails[0] as any).accountId : null),
    };
    this.applyDateFilterToPayload(filtersPayload);
    this.store.dispatch(new UpdateCPLFilterPayload(filtersPayload));
    this.store.dispatch(new FetchCPLTracking(filtersPayload));
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    this.gridApi.applyTransaction({ add: this.rowData });
    this.offset = 0;
  }

  search(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      if (!this.searchTerm || this.searchTerm.trim() === '') {
        this.gridApi.setQuickFilter(null);
        return
      }
      this.filtersPayload = {
        ...this.filtersPayload,
        pageNumber: 1,
        SearchText: this.searchTerm,
        AccountId: this.cplTrackForm.value.cplAccountName || (this.accountDetails.length > 0 ? (this.accountDetails[0] as any).accountId : null),
      };
      this.applyDateFilterToPayload(this.filtersPayload);
      this.store.dispatch(new UpdateCPLFilterPayload(this.filtersPayload));
      this.store.dispatch(new FetchCPLTracking(this.filtersPayload));
      this.offset = 0;
    }
  }

  clearSearch() {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.gridApi.setQuickFilter(null);
      this.filtersPayload = {
        ...this.filtersPayload,
        SearchText: this.searchTerm,
        AccountId: this.cplTrackForm.value.cplAccountName,
      };
      this.applyDateFilterToPayload(this.filtersPayload);
      this.store.dispatch(new UpdateCPLFilterPayload(this.filtersPayload));
      this.store.dispatch(new FetchCPLTracking(this.filtersPayload));
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
